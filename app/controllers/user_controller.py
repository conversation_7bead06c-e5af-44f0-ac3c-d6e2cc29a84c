from fastapi import HTT<PERSON>Exception, status  # type: ignore
from sqlalchemy.orm import Session  # type: ignore
from app.schemas.users_schema import UserCreate, LoginUser
from app.models import User
from app.core.security import hash_password, verify_password


class UserController:
    def register_user(self, db: Session, user: UserCreate):
        try:
            existing_user = db.query(User).filter(User.email == user.email).first()
            if existing_user:
                raise HTTPException(
                    status_code=status.HTTP_400_BAD_REQUEST,
                    detail="User already exists with this email",
                )
            hashed_pwd = hash_password(user.password)
            db_user = User(
                email=user.email,
                hashed_password=hashed_pwd,
                firstName=user.first_name,
                lastName=user.last_name,
            )

            db.add(db_user)
            db.commit()
            db.refresh(db_user)
            return {
                "status": "success",
                "message": "User registered successfully",
                "user_id": db_user.id,
            }
        except Exception as e:
            print(e)
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail="An error occurred while registering the user",
            )

    def login_user(self, db: Session, user: LoginUser):
        try:
            if user.email == "" or user.password == "":
                raise HTTPException(
                    status_code=status.HTTP_400_BAD_REQUEST,
                    detail="Email and password are required",
                )
            db_user = db.query(User).filter(User.email == user.email).first()
            if not db_user:
                raise HTTPException(
                    status_code=status.HTTP_404_NOT_FOUND,
                    detail="User not found",
                )
            if not verify_password(user.password, db_user.hashed_password):
                raise HTTPException(
                    status_code=status.HTTP_401_UNAUTHORIZED,
                    detail="Incorrect password",
                )
            return {
                "status": "success",
                "message": "User logged in successfully",
                "user_id": db_user.id,
            }
        except Exception as e:
            print(e)
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail="An error occurred while logging in the user",
            )
