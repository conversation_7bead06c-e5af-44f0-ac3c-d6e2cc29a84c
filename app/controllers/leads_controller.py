from app.schemas.leads_schema import LeadCreate
from app.services.scraper_service import ScraperService
from fastapi import HTT<PERSON>Ex<PERSON>, status  # type: ignore
from sqlalchemy.orm import Session  # type: ignore
from app.models import Lead
from app.tools.mcp_client import main
import asyncio


class LeadsController:
    def scrape_leads(self, db: Session, leads: LeadCreate):
        try:
            query = f"{leads.niche} in {leads.location}"

            target_count = 5
            saved_leads = []
            start_index = 1
            while len(saved_leads) < target_count:
                urls = ScraperService.google_search_api(
                    query, num_results=target_count, start=start_index
                )
                for url in urls:
                    lead_info = ScraperService.extract_lead_data(url)
                    if not lead_info["email"]:
                        continue
                    new_lead = Lead(
                        name=lead_info["name"],
                        title=lead_info["title"],
                        company=lead_info["company"],
                        companyWebsite=lead_info["companyWebsite"],
                        email=lead_info["email"],
                        linkedinUrl=lead_info["linkedinUrl"],
                        source=lead_info["source"],
                        created_at=lead_info["created_at"],
                        niche=leads.niche,
                        location=leads.location,
                        owner_id=leads.user_id,
                    )
                    db.add(new_lead)
                    saved_leads.append(new_lead)

                    if len(saved_leads) >= target_count:
                        break

                start_index += 10
            db.commit()
            for lead in saved_leads:
                db.refresh(lead)
            return {
                "message": "Leads scraped successfully",
                "leads": saved_leads,
            }

        except Exception as e:
            print(e)
            db.rollback()
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail="An error occurred while scraping leads",
            )

    def get_leads(self, db: Session, user_id: int):
        try:
            leads = db.query(Lead).filter(Lead.owner_id == user_id).all()
            return {
                "message": "Leads fetched successfully",
                "leads": leads,
            }
        except Exception as e:
            print(e)
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail="An error occurred while fetching leads",
            )

    def generate_email_for_lead(self, db: Session, niche: str):
        try:
            leads = db.query(Lead).filter(Lead.niche == niche).all()

            result = [
                {
                    "email": lead.email,
                    "generated_email": asyncio.run(main(lead.niche, lead.email)),
                }
                for lead in leads
                if lead.email
            ]

            return {"message": "Emails generated successfully", "leads": result}
        except Exception as e:
            print(e)
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail="An error occurred while generating emails",
            )
