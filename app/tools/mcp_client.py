from dotenv import load_dotenv
import os
import json

# Langchain Imports
from langchain_mcp_adapters.client import MultiServerMCPClient  # type: ignore
from langgraph.prebuilt import create_react_agent  # type: ignore
from langchain_google_genai import ChatGoogleGenerativeAI  # type: ignore
from langchain_core.messages import HumanMessage, AIMessage  # type: ignore


load_dotenv(override=True)
gemini_api_key = os.getenv("GEMMINE_API_KEY")


class CustomJsonEncoder(json.JSONEncoder):
    def default(self, obj):

        if hasattr(obj, "content"):
            return {"type": obj.__class__.__name__, "content": obj.content}
        return super().default(obj)


llm = ChatGoogleGenerativeAI(
    google_api_key=gemini_api_key,
    model="gemini-2.0-flash",
    temperature=0,
    max_retries=2,
)


async def main(niche: str, email: str):
    client = MultiServerMCPClient(
        {
            "generate_cold_email": {
                "command": "python",
                "args": ["app/tools/mcp_tools.py"],
                "transport": "stdio",
            },
        },
    )

    tools = await client.get_tools()
    agent = create_react_agent(llm, tools)

    prompt = (
        f"You are writing a professional, concise, and engaging cold email to connect with business owners "
        f"or decision-makers in the {niche} industry.\n\n"
        f"Email Goals:\n"
        f"- Introduce yourself as someone working in business development.\n"
        f"- Show genuine interest in their work within the {niche} space.\n"
        f"- Briefly explain how connecting could lead to mutually beneficial opportunities.\n"
        f"- Keep it warm, approachable, and non-pushy.\n"
        f"- Invite them to a quick call or meeting to explore synergies.\n\n"
        f"Email Requirements:\n"
        f"- Maintain a professional yet friendly tone.\n"
        f"- Avoid marketing-heavy language; focus on relationship-building.\n"
        f"- Avoid placeholders like [Company Name] or [Your Name].\n"
        f"- Write a compelling subject line relevant to {niche}.\n"
        f"- Keep the email short — ideally under 150 words.\n"
        f"- End with a clear, low-pressure call-to-action.\n"
        f"- Include a simple, credible signature if relevant.\n\n"
        f"Ensure the email feels personal and engaging, even if sent to multiple leads."
    )

    print(f"\n--- Constructed Prompt ---\n{prompt}\n")

    result = await agent.ainvoke(
        {"input": prompt, "messages": [HumanMessage(content=prompt)]}
    )
    print("Agent response:")
    print(json.dumps(result, indent=2, cls=CustomJsonEncoder))
    return json.dumps(result, indent=2, cls=CustomJsonEncoder)
