from passlib.context import CryptContext  # type: ignore
from jwcrypto import jwt  # type: ignore
import os
from dotenv import load_dotenv

pwd_context = CryptContext(schemes=["bcrypt"], deprecated="auto")

load_dotenv(override=True)

secret_key = os.getenv("JWT_SECRET")
algorithm = os.getenv("ALGORITHM")


def hash_password(password: str) -> str:
    return pwd_context.hash(password)


def verify_password(plain_password: str, hashed_password: str) -> bool:
    return pwd_context.verify(plain_password, hashed_password)


def generate_token(data: dict) -> str:
    return jwt.encode(data, secret_key, algorithm=algorithm)
