import os
import re
import requests
from bs4 import BeautifulSoup  # type: ignore
from typing import List, Dict
from dotenv import load_dotenv
from datetime import datetime

load_dotenv(override=True)

GOOGLE_SEARCH_API_KEY = os.getenv("GOOGLE_SEARCH_API_KEY")
GOOGLE_SEARCH_API_CX = os.getenv("GOOGLE_SEARCH_ENGINE_ID")

if not GOOGLE_SEARCH_API_KEY or not GOOGLE_SEARCH_API_CX:
    raise ValueError("GOOGLE_SEARCH_API_KEY or GOOGLE_SEARCH_API_CX is not set")


class ScraperService:
    EXCLUDED_SITES = ["reddit.com", "quora.com", "facebook.com", "instagram.com"]

    @staticmethod
    def google_search_api(
        query: str, num_results: int = 10, start: int = 1
    ) -> List[str]:
        """
        Performs a Google search using the Google Search API and returns a list of result links,
        excluding unwanted sites.
        """
        exclusions = " ".join(
            [f"-site:{site}" for site in ScraperService.EXCLUDED_SITES]
        )
        full_query = f"{query} {exclusions}"

        url = "https://www.googleapis.com/customsearch/v1"
        params = {
            "key": GOOGLE_SEARCH_API_KEY,
            "cx": GOOGLE_SEARCH_API_CX,
            "q": full_query,
            "num": min(num_results, 10),
            "start": start,
        }

        response = requests.get(url, params=params)
        response.raise_for_status()
        data = response.json()
        return [item["link"] for item in data.get("items", [])]

    @staticmethod
    def extract_lead_data(url: str) -> Dict:
        """
        Extracts basic lead information from a webpage.
        """
        lead_data = {
            "name": None,
            "title": None,
            "company": None,
            "companyWebsite": url,
            "email": None,
            "linkedinUrl": None,
            "source": url,
            "created_at": datetime.now().strftime("%Y-%m-%d %H:%M:%S"),
        }

        try:
            html = requests.get(url, timeout=10, headers={"User-Agent": "Mozilla/5.0"})
            soup = BeautifulSoup(html.text, "html.parser")

            email_match = re.search(
                r"[A-Za-z0-9._%+-]+@[A-Za-z0-9.-]+\.[A-Za-z]{2,}", soup.get_text()
            )
            if email_match:
                lead_data["email"] = email_match.group()

            linkedin_match = re.search(
                r"https://(www\.)?linkedin\.com/[^\s'\"]+", html.text
            )
            if linkedin_match:
                lead_data["linkedinUrl"] = linkedin_match.group()
            company_name = None

            og_site_name = soup.find("meta", property="og:site_name")
            if og_site_name and og_site_name.get("content"):
                company_name = og_site_name["content"].strip()

            if company_name:
                lead_data["company"] = company_name

            if soup.title:
                lead_data["title"] = soup.title.string.strip()

        except Exception as e:
            print(f"Error scraping {url}: {e}")

        return lead_data
