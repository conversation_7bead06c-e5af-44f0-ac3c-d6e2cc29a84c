from app.schemas.leads_schema import Lead<PERSON><PERSON>
from fastapi import APIRouter, Depends # type: ignore
from sqlalchemy.orm import Session # type: ignore
from app.core.db import get_db
from app.controllers.leads_controller import LeadsController

controller = LeadsController()

router = APIRouter()

@router.post("/scrape")
def scrape(leads: LeadCreate, db: Session = Depends(get_db)):
    return controller.scrape_leads(db, leads)

@router.get("/getleads")
def get_leads(user_id: int, db: Session = Depends(get_db)):
    return controller.get_leads(db, user_id)

@router.get("/generateemail")
def generate_email_for_lead(niche: str, db: Session = Depends(get_db)):
    return controller.generate_email_for_lead(db, niche)
