# app/routes/user_routes.py
from fastapi import APIRouter, Depends # type: ignore
from sqlalchemy.orm import Session # type: ignore
from app.controllers.user_controller import UserController
from app.core.db import get_db
from app.schemas.users_schema import User<PERSON>reate, LoginUser

router = APIRouter()
controller = UserController()

@router.post("/register")
def register(user: UserCreate, db: Session = Depends(get_db)):
    return controller.register_user(db, user)

@router.post("/login")
def login(user: LoginUser, db: Session = Depends(get_db)):
    return controller.login_user(db, user)