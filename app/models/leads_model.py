from datetime import datetime
from sqlalchemy import Column, Integer, String, ForeignKey  # type: ignore
from sqlalchemy.orm import relationship  # type: ignore
from app.core.db import Base


class Lead(Base):
    __tablename__ = "leads"
    id = Column(Integer, primary_key=True, index=True)
    name = Column(String, nullable=True)
    title = Column(String, nullable=True)
    company = Column(String, nullable=True)
    companyWebsite = Column(String, nullable=True)
    email = Column(String, nullable=True, index=True)
    linkedinUrl = Column(String, nullable=True)
    source = Column(String, nullable=True)
    owner_id = Column(Integer, ForeignKey("users.id"))
    owner = relationship("User", back_populates="leads")
    created_at = Column(String, default=datetime.now().strftime("%Y-%m-%d %H:%M:%S"))
    niche = Column(String, nullable=True)
    location = Column(String, nullable=True)
