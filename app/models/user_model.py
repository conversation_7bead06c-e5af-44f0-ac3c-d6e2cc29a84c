from sqlalchemy import Column, Integer, String  # type: ignore
from sqlalchemy.orm import relationship  # type: ignore
from app.core.db import Base

class User(Base):
    __tablename__ = "users"
    id = Column(Integer, primary_key=True, index=True)
    email = Column(String, unique=True, index=True)
    firstName = Column(String,nullable=False)
    lastName = Column(String,nullable=False)
    hashed_password = Column(String,nullable=False)
    loginType = Column(String, default="local")
    leads = relationship("Lead", back_populates="owner", cascade="all, delete-orphan")
