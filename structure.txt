cold_email_lead_generator/
│
├── app/
│   ├── __init__.py
│   ├── models/
│   │   ├── __init__.py
│   │   └── user.py
│   │   └── lead.py
│   │   └── email_log.py
│   │
│   ├── controllers/
│   │   └── lead_controller.py
│   │   └── email_controller.py
│   │
│   ├── services/
│   │   └── scraper_service.py
│   │   └── email_generator_service.py
│   │   └── tracking_service.py
│   │
│   ├── routes/
│   │   └── lead_routes.py
│   │   └── email_routes.py
│   │
│   └── core/
│       ├── __init__.py
│       └── config.py
│       └── db.py
│
├── main.py
├── requirements.txt
├── .gitignore
├── Dockerfile
├── .env
└── README.md
