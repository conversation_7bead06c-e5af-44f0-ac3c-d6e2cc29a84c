from fastapi import FastAPI  # type: ignore
from app.core.db import engine
from fastapi.middleware.cors import CORSMiddleware  # type: ignore
import uvicorn  # type: ignore
from app.routes.user_routes import router as user_router
from app.routes.leads_routes import router as leads_router
import app.models as models

app = FastAPI(title="Cold Email & Lead Generator", version="1.0.0")
models.Base.metadata.create_all(bind=engine)


app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)


app.include_router(user_router)
app.include_router(leads_router)

@app.get("/")
def root():
    return {"message": "Welcome to Cold Email & Lead Generator API"}


if __name__ == "__main__":
    try:
        uvicorn.run(app, port=8000)
    except:
        print("Error")
