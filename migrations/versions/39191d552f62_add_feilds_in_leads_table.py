"""add-feilds in leads table

Revision ID: 39191d552f62
Revises: 550be3bc0df5
Create Date: 2025-08-15 11:13:49.928206

"""
from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision: str = '39191d552f62'
down_revision: Union[str, Sequence[str], None] = '550be3bc0df5'
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    """Upgrade schema."""
    # ### commands auto generated by Alembic - please adjust! ###
    op.add_column('leads', sa.Column('niche', sa.String(), nullable=True))
    op.add_column('leads', sa.Column('location', sa.String(), nullable=True))
    # ### end Alembic commands ###


def downgrade() -> None:
    """Downgrade schema."""
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_column('leads', 'location')
    op.drop_column('leads', 'niche')
    # ### end Alembic commands ###
