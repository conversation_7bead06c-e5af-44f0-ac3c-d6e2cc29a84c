"""add-feilds in leads table

Revision ID: 550be3bc0df5
Revises: 
Create Date: 2025-08-11 02:34:22.164336

"""
from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision: str = '550be3bc0df5'
down_revision: Union[str, Sequence[str], None] = None
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    """Upgrade schema."""
    # ### commands auto generated by Alembic - please adjust! ###
    op.add_column('leads', sa.Column('name', sa.String(), nullable=True))
    op.add_column('leads', sa.Column('title', sa.String(), nullable=True))
    op.add_column('leads', sa.Column('company', sa.String(), nullable=True))
    op.add_column('leads', sa.Column('companyWebsite', sa.String(), nullable=True))
    op.add_column('leads', sa.Column('linkedinUrl', sa.String(), nullable=True))
    op.add_column('leads', sa.Column('source', sa.String(), nullable=True))
    op.add_column('leads', sa.Column('created_at', sa.String(), nullable=True))
    op.create_index(op.f('ix_leads_email'), 'leads', ['email'], unique=False)
    op.drop_column('leads', 'company_name')
    # ### end Alembic commands ###


def downgrade() -> None:
    """Downgrade schema."""
    # ### commands auto generated by Alembic - please adjust! ###
    op.add_column('leads', sa.Column('company_name', sa.VARCHAR(), autoincrement=False, nullable=True))
    op.drop_index(op.f('ix_leads_email'), table_name='leads')
    op.drop_column('leads', 'created_at')
    op.drop_column('leads', 'source')
    op.drop_column('leads', 'linkedinUrl')
    op.drop_column('leads', 'companyWebsite')
    op.drop_column('leads', 'company')
    op.drop_column('leads', 'title')
    op.drop_column('leads', 'name')
    # ### end Alembic commands ###
